-- MemberWorkoutPrograms AssignedDate Constraint Düzeltme Script'i
-- Bu script mevcut production veritabanında çalıştırılmalıdır

USE [GymProject]
GO

PRINT 'AssignedDate constraint düzeltme işlemi başlatılıyor...'

-- 1. Mevcut constraint'i kaldır
IF EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_MemberWorkoutPrograms_AssignedDate')
BEGIN
    PRINT 'Mevcut CK_MemberWorkoutPrograms_AssignedDate constraint kaldırılıyor...'
    ALTER TABLE [dbo].[MemberWorkoutPrograms]
    DROP CONSTRAINT [CK_MemberWorkoutPrograms_AssignedDate]
    PRINT 'Eski constraint kaldırıldı.'
END
ELSE
BEGIN
    PRINT 'CK_MemberWorkoutPrograms_AssignedDate constraint bulunamadı.'
END

-- 2. Yeni constraint'i ekle (5 saniye tolerans ile)
PRINT 'Yeni constraint ekleniyor (5 saniye tolerans ile)...'
ALTER TABLE [dbo].[MemberWorkoutPrograms]
ADD CONSTRAINT [CK_MemberWorkoutPrograms_AssignedDate] 
CHECK ([AssignedDate] <= DATEADD(SECOND, 5, GETDATE()))
GO

-- 3. Mevcut verileri kontrol et
PRINT 'Mevcut veriler kontrol ediliyor...'
DECLARE @InvalidRecords INT
SELECT @InvalidRecords = COUNT(*)
FROM [dbo].[MemberWorkoutPrograms]
WHERE [AssignedDate] > DATEADD(SECOND, 5, GETDATE())

IF @InvalidRecords > 0
BEGIN
    PRINT 'UYARI: ' + CAST(@InvalidRecords AS VARCHAR(10)) + ' adet gelecek tarihli kayıt bulundu!'
    PRINT 'Bu kayıtları düzeltmek için aşağıdaki script''i çalıştırın:'
    PRINT 'UPDATE [dbo].[MemberWorkoutPrograms] SET [AssignedDate] = GETDATE() WHERE [AssignedDate] > GETDATE()'
END
ELSE
BEGIN
    PRINT 'Tüm kayıtlar geçerli tarih aralığında.'
END

-- 4. Test et
PRINT 'Constraint test ediliyor...'
BEGIN TRY
    -- Geçerli bir test kaydı eklemeyi dene
    DECLARE @TestMemberID INT = (SELECT TOP 1 MemberID FROM Members WHERE IsActive = 1)
    DECLARE @TestProgramID INT = (SELECT TOP 1 WorkoutProgramTemplateID FROM WorkoutProgramTemplates WHERE IsActive = 1)
    DECLARE @TestCompanyID INT = (SELECT TOP 1 CompanyID FROM Companies WHERE IsActive = 1)
    
    IF @TestMemberID IS NOT NULL AND @TestProgramID IS NOT NULL AND @TestCompanyID IS NOT NULL
    BEGIN
        INSERT INTO [dbo].[MemberWorkoutPrograms] 
        (MemberID, WorkoutProgramTemplateID, CompanyID, StartDate, IsActive)
        VALUES 
        (@TestMemberID, @TestProgramID, @TestCompanyID, GETDATE(), 0) -- IsActive=0 test kaydı olduğunu belirtir
        
        PRINT 'Test kaydı başarıyla eklendi.'
        
        -- Test kaydını sil
        DELETE FROM [dbo].[MemberWorkoutPrograms] 
        WHERE MemberID = @TestMemberID 
        AND WorkoutProgramTemplateID = @TestProgramID 
        AND IsActive = 0
        
        PRINT 'Test kaydı temizlendi.'
    END
    ELSE
    BEGIN
        PRINT 'Test için gerekli veriler bulunamadı, constraint test atlandı.'
    END
END TRY
BEGIN CATCH
    PRINT 'Test sırasında hata oluştu: ' + ERROR_MESSAGE()
END CATCH

PRINT 'AssignedDate constraint düzeltme işlemi tamamlandı!'
PRINT ''
PRINT 'Değişiklik Özeti:'
PRINT '- Eski constraint: [AssignedDate] <= GETDATE()'
PRINT '- Yeni constraint: [AssignedDate] <= DATEADD(SECOND, 5, GETDATE())'
PRINT '- Bu değişiklik client-server zaman farkından kaynaklanan hataları önler.'
PRINT ''
PRINT 'Backend kodunda da AssignedDate artık otomatik olarak veritabanı tarafından set edilecek.'
GO
