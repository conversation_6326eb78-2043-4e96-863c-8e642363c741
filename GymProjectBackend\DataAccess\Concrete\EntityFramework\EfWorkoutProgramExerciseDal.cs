using Core.DataAccess.EntityFramework;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using Entities.Concrete;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfWorkoutProgramExerciseDal : EfCompanyEntityRepositoryBase<WorkoutProgramExercise, GymContext>, IWorkoutProgramExerciseDal
    {
        private readonly ICompanyContext _companyContext;

        public EfWorkoutProgramExerciseDal(ICompanyContext companyContext) : base(companyContext)
        {
            _companyContext = companyContext;
        }
    }
}
