-- Workout Program Multi-Tenant Güvenlik Düzeltme Migration
-- Bu migration WorkoutProgramDay ve WorkoutProgramExercise tablolarına CompanyID ekler
-- Çok kiracılı sistem güvenliği için kritik düzeltme

USE [GymProject]
GO

PRINT 'Workout Program Multi-Tenant güvenlik düzeltmesi başlatılıyor...'

-- 1. WorkoutProgramDays tablosuna CompanyID ekleme
IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE Name = N'CompanyID' AND Object_ID = Object_ID(N'WorkoutProgramDays'))
BEGIN
    PRINT 'WorkoutProgramDays tablosuna CompanyID alanı ekleniyor...'
    
    ALTER TABLE [dbo].[WorkoutProgramDays]
    ADD [CompanyID] [int] NULL
    
    PRINT 'CompanyID alanı eklendi.'
END
ELSE
BEGIN
    PRINT 'WorkoutProgramDays tablosunda CompanyID alanı zaten mevcut.'
END

-- 2. WorkoutProgramExercises tablosuna CompanyID ekleme
IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE Name = N'CompanyID' AND Object_ID = Object_ID(N'WorkoutProgramExercises'))
BEGIN
    PRINT 'WorkoutProgramExercises tablosuna CompanyID alanı ekleniyor...'
    
    ALTER TABLE [dbo].[WorkoutProgramExercises]
    ADD [CompanyID] [int] NULL
    
    PRINT 'CompanyID alanı eklendi.'
END
ELSE
BEGIN
    PRINT 'WorkoutProgramExercises tablosunda CompanyID alanı zaten mevcut.'
END

-- 3. WorkoutProgramDays tablosuna DeletedDate ve UpdatedDate ekleme
IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE Name = N'DeletedDate' AND Object_ID = Object_ID(N'WorkoutProgramDays'))
BEGIN
    PRINT 'WorkoutProgramDays tablosuna DeletedDate alanı ekleniyor...'
    
    ALTER TABLE [dbo].[WorkoutProgramDays]
    ADD [DeletedDate] [datetime2](7) NULL
    
    PRINT 'DeletedDate alanı eklendi.'
END

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE Name = N'UpdatedDate' AND Object_ID = Object_ID(N'WorkoutProgramDays'))
BEGIN
    PRINT 'WorkoutProgramDays tablosuna UpdatedDate alanı ekleniyor...'
    
    ALTER TABLE [dbo].[WorkoutProgramDays]
    ADD [UpdatedDate] [datetime2](7) NULL
    
    PRINT 'UpdatedDate alanı eklendi.'
END

-- 4. WorkoutProgramExercises tablosuna DeletedDate ve UpdatedDate ekleme
IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE Name = N'DeletedDate' AND Object_ID = Object_ID(N'WorkoutProgramExercises'))
BEGIN
    PRINT 'WorkoutProgramExercises tablosuna DeletedDate alanı ekleniyor...'
    
    ALTER TABLE [dbo].[WorkoutProgramExercises]
    ADD [DeletedDate] [datetime2](7) NULL
    
    PRINT 'DeletedDate alanı eklendi.'
END

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE Name = N'UpdatedDate' AND Object_ID = Object_ID(N'WorkoutProgramExercises'))
BEGIN
    PRINT 'WorkoutProgramExercises tablosuna UpdatedDate alanı ekleniyor...'
    
    ALTER TABLE [dbo].[WorkoutProgramExercises]
    ADD [UpdatedDate] [datetime2](7) NULL
    
    PRINT 'UpdatedDate alanı eklendi.'
END

-- 5. Mevcut verilere CompanyID değerlerini atama
PRINT 'Mevcut verilere CompanyID değerleri atanıyor...'

-- WorkoutProgramDays için CompanyID güncelleme
UPDATE wpd 
SET wpd.CompanyID = wpt.CompanyID
FROM [dbo].[WorkoutProgramDays] wpd
INNER JOIN [dbo].[WorkoutProgramTemplates] wpt ON wpd.WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID
WHERE wpd.CompanyID IS NULL

DECLARE @UpdatedDays INT = @@ROWCOUNT
PRINT 'WorkoutProgramDays tablosunda ' + CAST(@UpdatedDays AS VARCHAR(10)) + ' kayıt güncellendi.'

-- WorkoutProgramExercises için CompanyID güncelleme
UPDATE wpe 
SET wpe.CompanyID = wpt.CompanyID
FROM [dbo].[WorkoutProgramExercises] wpe
INNER JOIN [dbo].[WorkoutProgramDays] wpd ON wpe.WorkoutProgramDayID = wpd.WorkoutProgramDayID
INNER JOIN [dbo].[WorkoutProgramTemplates] wpt ON wpd.WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID
WHERE wpe.CompanyID IS NULL

DECLARE @UpdatedExercises INT = @@ROWCOUNT
PRINT 'WorkoutProgramExercises tablosunda ' + CAST(@UpdatedExercises AS VARCHAR(10)) + ' kayıt güncellendi.'

-- 6. CompanyID alanlarını NOT NULL yapma
PRINT 'CompanyID alanları NOT NULL yapılıyor...'

ALTER TABLE [dbo].[WorkoutProgramDays]
ALTER COLUMN [CompanyID] [int] NOT NULL

ALTER TABLE [dbo].[WorkoutProgramExercises]
ALTER COLUMN [CompanyID] [int] NOT NULL

PRINT 'CompanyID alanları NOT NULL yapıldı.'

-- 7. Foreign Key constraint'leri ekleme
IF NOT EXISTS (SELECT 1 FROM sys.foreign_keys WHERE name = 'FK_WorkoutProgramDays_Companies')
BEGIN
    PRINT 'WorkoutProgramDays için Company foreign key ekleniyor...'
    
    ALTER TABLE [dbo].[WorkoutProgramDays]
    ADD CONSTRAINT [FK_WorkoutProgramDays_Companies] FOREIGN KEY([CompanyID]) 
        REFERENCES [dbo].[Companies] ([CompanyID])
    
    PRINT 'Foreign key eklendi.'
END

IF NOT EXISTS (SELECT 1 FROM sys.foreign_keys WHERE name = 'FK_WorkoutProgramExercises_Companies')
BEGIN
    PRINT 'WorkoutProgramExercises için Company foreign key ekleniyor...'
    
    ALTER TABLE [dbo].[WorkoutProgramExercises]
    ADD CONSTRAINT [FK_WorkoutProgramExercises_Companies] FOREIGN KEY([CompanyID]) 
        REFERENCES [dbo].[Companies] ([CompanyID])
    
    PRINT 'Foreign key eklendi.'
END

-- 8. Performans indexleri ekleme
PRINT 'Performans indexleri ekleniyor...'

-- WorkoutProgramDays için company bazlı index
IF NOT EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'IX_WorkoutProgramDays_CompanyID_TemplateID')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_WorkoutProgramDays_CompanyID_TemplateID] 
    ON [dbo].[WorkoutProgramDays] ([CompanyID], [WorkoutProgramTemplateID])
    INCLUDE ([DayNumber], [DayName], [IsRestDay])
    
    PRINT 'WorkoutProgramDays index eklendi.'
END

-- WorkoutProgramExercises için company bazlı index
IF NOT EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'IX_WorkoutProgramExercises_CompanyID_DayID')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_WorkoutProgramExercises_CompanyID_DayID] 
    ON [dbo].[WorkoutProgramExercises] ([CompanyID], [WorkoutProgramDayID])
    INCLUDE ([ExerciseType], [ExerciseID], [OrderIndex])
    
    PRINT 'WorkoutProgramExercises index eklendi.'
END

-- 9. Veri bütünlüğü kontrolü
PRINT 'Veri bütünlüğü kontrol ediliyor...'

DECLARE @OrphanDays INT = (
    SELECT COUNT(*) 
    FROM [dbo].[WorkoutProgramDays] wpd
    LEFT JOIN [dbo].[WorkoutProgramTemplates] wpt ON wpd.WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID
    WHERE wpt.WorkoutProgramTemplateID IS NULL
)

DECLARE @OrphanExercises INT = (
    SELECT COUNT(*) 
    FROM [dbo].[WorkoutProgramExercises] wpe
    LEFT JOIN [dbo].[WorkoutProgramDays] wpd ON wpe.WorkoutProgramDayID = wpd.WorkoutProgramDayID
    WHERE wpd.WorkoutProgramDayID IS NULL
)

IF @OrphanDays > 0
BEGIN
    PRINT 'UYARI: ' + CAST(@OrphanDays AS VARCHAR(10)) + ' adet orphan WorkoutProgramDay kaydı bulundu!'
END

IF @OrphanExercises > 0
BEGIN
    PRINT 'UYARI: ' + CAST(@OrphanExercises AS VARCHAR(10)) + ' adet orphan WorkoutProgramExercise kaydı bulundu!'
END

PRINT 'Workout Program Multi-Tenant güvenlik düzeltmesi tamamlandı!'
PRINT ''
PRINT 'Değişiklik Özeti:'
PRINT '- WorkoutProgramDays tablosuna CompanyID, DeletedDate, UpdatedDate eklendi'
PRINT '- WorkoutProgramExercises tablosuna CompanyID, DeletedDate, UpdatedDate eklendi'
PRINT '- Mevcut veriler güncellendi'
PRINT '- Foreign key constraint''ler eklendi'
PRINT '- Performans indexleri eklendi'
PRINT '- Çok kiracılı sistem güvenliği sağlandı'
GO
