using Core.DataAccess.EntityFramework;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using Entities.Concrete;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfWorkoutProgramDayDal : EfCompanyEntityRepositoryBase<WorkoutProgramDay, GymContext>, IWorkoutProgramDayDal
    {
        private readonly ICompanyContext _companyContext;

        public EfWorkoutProgramDayDal(ICompanyContext companyContext) : base(companyContext)
        {
            _companyContext = companyContext;
        }
    }
}
