-- MemberWorkoutPrograms AssignedDate Constraint Düzeltme Migration
-- Bu migration mevcut production veritabanında AssignedDate constraint sorununu çözer
-- Sorun: DateTime.Now (client) vs GETDATE() (server) zaman farkından kaynaklanan constraint ihlali

USE [GymProject]
GO

PRINT 'MemberWorkoutPrograms AssignedDate constraint düzeltme migration başlatılıyor...'

-- Mevcut constraint'i kontrol et ve gerekirse güncelle
IF EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_MemberWorkoutPrograms_AssignedDate')
BEGIN
    PRINT 'Mevcut CK_MemberWorkoutPrograms_AssignedDate constraint bulundu.'
    
    -- Constraint tanımını kontrol et
    DECLARE @ConstraintDefinition NVARCHAR(MAX)
    SELECT @ConstraintDefinition = definition 
    FROM sys.check_constraints 
    WHERE name = 'CK_MemberWorkoutPrograms_AssignedDate'
    
    -- Eğer eski constraint ise (tolerans yoksa) güncelle
    IF @ConstraintDefinition NOT LIKE '%DATEADD%'
    BEGIN
        PRINT 'Eski constraint bulundu, güncelleniyor...'
        
        -- Eski constraint'i kaldır
        ALTER TABLE [dbo].[MemberWorkoutPrograms]
        DROP CONSTRAINT [CK_MemberWorkoutPrograms_AssignedDate]
        
        -- Yeni constraint'i ekle (5 saniye tolerans ile)
        ALTER TABLE [dbo].[MemberWorkoutPrograms]
        ADD CONSTRAINT [CK_MemberWorkoutPrograms_AssignedDate] 
        CHECK ([AssignedDate] <= DATEADD(SECOND, 5, GETDATE()))
        
        PRINT 'Constraint başarıyla güncellendi.'
    END
    ELSE
    BEGIN
        PRINT 'Constraint zaten güncel durumda.'
    END
END
ELSE
BEGIN
    PRINT 'CK_MemberWorkoutPrograms_AssignedDate constraint bulunamadı, ekleniyor...'
    
    -- Constraint'i ekle
    ALTER TABLE [dbo].[MemberWorkoutPrograms]
    ADD CONSTRAINT [CK_MemberWorkoutPrograms_AssignedDate] 
    CHECK ([AssignedDate] <= DATEADD(SECOND, 5, GETDATE()))
    
    PRINT 'Constraint başarıyla eklendi.'
END

-- Mevcut geçersiz verileri düzelt (eğer varsa)
DECLARE @InvalidRecords INT
SELECT @InvalidRecords = COUNT(*)
FROM [dbo].[MemberWorkoutPrograms]
WHERE [AssignedDate] > DATEADD(SECOND, 5, GETDATE())

IF @InvalidRecords > 0
BEGIN
    PRINT 'UYARI: ' + CAST(@InvalidRecords AS VARCHAR(10)) + ' adet gelecek tarihli kayıt bulundu!'
    PRINT 'Bu kayıtlar düzeltiliyor...'
    
    UPDATE [dbo].[MemberWorkoutPrograms] 
    SET [AssignedDate] = GETDATE() 
    WHERE [AssignedDate] > GETDATE()
    
    PRINT 'Geçersiz kayıtlar düzeltildi.'
END
ELSE
BEGIN
    PRINT 'Tüm kayıtlar geçerli tarih aralığında.'
END

PRINT 'MemberWorkoutPrograms AssignedDate constraint düzeltme migration tamamlandı!'
PRINT ''
PRINT 'Değişiklik Özeti:'
PRINT '- Constraint: [AssignedDate] <= DATEADD(SECOND, 5, GETDATE())'
PRINT '- Bu değişiklik client-server zaman farkından kaynaklanan hataları önler.'
PRINT '- Backend kodunda AssignedDate artık veritabanı tarafından otomatik set edilir.'
GO
