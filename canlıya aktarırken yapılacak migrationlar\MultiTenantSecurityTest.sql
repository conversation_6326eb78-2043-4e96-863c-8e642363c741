-- Multi-Tenant Gü<PERSON> Test Script
-- Bu script migration sonrası çok kiracılı sistem güvenliğini test eder

USE [GymProject]
GO

PRINT '=== ÇOK KİRACILI SİSTEM GÜVENLİK TESTİ ==='
PRINT 'Test başlatılıyor...'
PRINT ''

-- 1. <PERSON><PERSON>lo yapısı kontrolü
PRINT '1. TABLO YAPISI KONTROLÜ'
PRINT '========================'

-- WorkoutProgramDays tablosu kontrolü
IF EXISTS (SELECT 1 FROM sys.columns WHERE Name = N'CompanyID' AND Object_ID = Object_ID(N'WorkoutProgramDays'))
BEGIN
    PRINT '✅ WorkoutProgramDays.CompanyID alanı mevcut'
END
ELSE
BEGIN
    PRINT '❌ WorkoutProgramDays.CompanyID alanı eksik!'
END

-- WorkoutProgramExercises tablosu kontrolü
IF EXISTS (SELECT 1 FROM sys.columns WHERE Name = N'CompanyID' AND Object_ID = Object_ID(N'WorkoutProgramExercises'))
BEGIN
    PRINT '✅ WorkoutProgramExercises.CompanyID alanı mevcut'
END
ELSE
BEGIN
    PRINT '❌ WorkoutProgramExercises.CompanyID alanı eksik!'
END

PRINT ''

-- 2. Foreign Key kontrolü
PRINT '2. FOREIGN KEY KONTROLÜ'
PRINT '======================='

IF EXISTS (SELECT 1 FROM sys.foreign_keys WHERE name = 'FK_WorkoutProgramDays_Companies')
BEGIN
    PRINT '✅ WorkoutProgramDays -> Companies foreign key mevcut'
END
ELSE
BEGIN
    PRINT '❌ WorkoutProgramDays -> Companies foreign key eksik!'
END

IF EXISTS (SELECT 1 FROM sys.foreign_keys WHERE name = 'FK_WorkoutProgramExercises_Companies')
BEGIN
    PRINT '✅ WorkoutProgramExercises -> Companies foreign key mevcut'
END
ELSE
BEGIN
    PRINT '❌ WorkoutProgramExercises -> Companies foreign key eksik!'
END

PRINT ''

-- 3. Index kontrolü
PRINT '3. PERFORMANS İNDEX KONTROLÜ'
PRINT '============================'

IF EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'IX_WorkoutProgramDays_CompanyID_TemplateID')
BEGIN
    PRINT '✅ WorkoutProgramDays performans index mevcut'
END
ELSE
BEGIN
    PRINT '⚠️ WorkoutProgramDays performans index eksik'
END

IF EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'IX_WorkoutProgramExercises_CompanyID_DayID')
BEGIN
    PRINT '✅ WorkoutProgramExercises performans index mevcut'
END
ELSE
BEGIN
    PRINT '⚠️ WorkoutProgramExercises performans index eksik'
END

PRINT ''

-- 4. Veri bütünlüğü kontrolü
PRINT '4. VERİ BÜTÜNLÜĞÜ KONTROLÜ'
PRINT '========================='

-- WorkoutProgramDays veri kontrolü
DECLARE @InvalidDays INT = (
    SELECT COUNT(*) 
    FROM WorkoutProgramDays wpd
    LEFT JOIN WorkoutProgramTemplates wpt ON wpd.WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID
    WHERE wpd.CompanyID != wpt.CompanyID OR wpt.WorkoutProgramTemplateID IS NULL
)

IF @InvalidDays = 0
BEGIN
    PRINT '✅ WorkoutProgramDays veri bütünlüğü sağlanmış'
END
ELSE
BEGIN
    PRINT '❌ WorkoutProgramDays veri bütünlüğü sorunu: ' + CAST(@InvalidDays AS VARCHAR(10)) + ' geçersiz kayıt'
END

-- WorkoutProgramExercises veri kontrolü
DECLARE @InvalidExercises INT = (
    SELECT COUNT(*) 
    FROM WorkoutProgramExercises wpe
    LEFT JOIN WorkoutProgramDays wpd ON wpe.WorkoutProgramDayID = wpd.WorkoutProgramDayID
    WHERE wpe.CompanyID != wpd.CompanyID OR wpd.WorkoutProgramDayID IS NULL
)

IF @InvalidExercises = 0
BEGIN
    PRINT '✅ WorkoutProgramExercises veri bütünlüğü sağlanmış'
END
ELSE
BEGIN
    PRINT '❌ WorkoutProgramExercises veri bütünlüğü sorunu: ' + CAST(@InvalidExercises AS VARCHAR(10)) + ' geçersiz kayıt'
END

-- NULL CompanyID kontrolü
DECLARE @NullCompanyDays INT = (SELECT COUNT(*) FROM WorkoutProgramDays WHERE CompanyID IS NULL)
DECLARE @NullCompanyExercises INT = (SELECT COUNT(*) FROM WorkoutProgramExercises WHERE CompanyID IS NULL)

IF @NullCompanyDays = 0 AND @NullCompanyExercises = 0
BEGIN
    PRINT '✅ Tüm kayıtlarda CompanyID değeri mevcut'
END
ELSE
BEGIN
    IF @NullCompanyDays > 0
        PRINT '❌ WorkoutProgramDays tablosunda ' + CAST(@NullCompanyDays AS VARCHAR(10)) + ' NULL CompanyID'
    IF @NullCompanyExercises > 0
        PRINT '❌ WorkoutProgramExercises tablosunda ' + CAST(@NullCompanyExercises AS VARCHAR(10)) + ' NULL CompanyID'
END

PRINT ''

-- 5. Çok kiracılı izolasyon testi
PRINT '5. ÇOK KİRACILI İZOLASYON TESTİ'
PRINT '=============================='

-- Farklı şirketlerin verilerinin karışıp karışmadığını kontrol et
DECLARE @CrossCompanyDays INT = (
    SELECT COUNT(*)
    FROM WorkoutProgramDays wpd
    JOIN WorkoutProgramTemplates wpt ON wpd.WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID
    WHERE wpd.CompanyID != wpt.CompanyID
)

DECLARE @CrossCompanyExercises INT = (
    SELECT COUNT(*)
    FROM WorkoutProgramExercises wpe
    JOIN WorkoutProgramDays wpd ON wpe.WorkoutProgramDayID = wpd.WorkoutProgramDayID
    WHERE wpe.CompanyID != wpd.CompanyID
)

IF @CrossCompanyDays = 0 AND @CrossCompanyExercises = 0
BEGIN
    PRINT '✅ Çok kiracılı izolasyon sağlanmış - şirket verileri karışmamış'
END
ELSE
BEGIN
    IF @CrossCompanyDays > 0
        PRINT '❌ WorkoutProgramDays çapraz şirket verisi: ' + CAST(@CrossCompanyDays AS VARCHAR(10)) + ' kayıt'
    IF @CrossCompanyExercises > 0
        PRINT '❌ WorkoutProgramExercises çapraz şirket verisi: ' + CAST(@CrossCompanyExercises AS VARCHAR(10)) + ' kayıt'
END

PRINT ''

-- 6. İstatistikler
PRINT '6. İSTATİSTİKLER'
PRINT '==============='

DECLARE @TotalCompanies INT = (SELECT COUNT(*) FROM Companies WHERE IsActive = 1)
DECLARE @TotalTemplates INT = (SELECT COUNT(*) FROM WorkoutProgramTemplates WHERE IsActive = 1)
DECLARE @TotalDays INT = (SELECT COUNT(*) FROM WorkoutProgramDays)
DECLARE @TotalExercises INT = (SELECT COUNT(*) FROM WorkoutProgramExercises)

PRINT 'Aktif Şirket Sayısı: ' + CAST(@TotalCompanies AS VARCHAR(10))
PRINT 'Toplam Program Şablonu: ' + CAST(@TotalTemplates AS VARCHAR(10))
PRINT 'Toplam Program Günü: ' + CAST(@TotalDays AS VARCHAR(10))
PRINT 'Toplam Program Egzersizi: ' + CAST(@TotalExercises AS VARCHAR(10))

-- Şirket bazlı dağılım
PRINT ''
PRINT 'Şirket Bazlı Program Dağılımı:'
SELECT 
    c.CompanyName,
    COUNT(DISTINCT wpt.WorkoutProgramTemplateID) as ProgramSayisi,
    COUNT(DISTINCT wpd.WorkoutProgramDayID) as GunSayisi,
    COUNT(DISTINCT wpe.WorkoutProgramExerciseID) as EgzersizSayisi
FROM Companies c
LEFT JOIN WorkoutProgramTemplates wpt ON c.CompanyID = wpt.CompanyID AND wpt.IsActive = 1
LEFT JOIN WorkoutProgramDays wpd ON wpt.WorkoutProgramTemplateID = wpd.WorkoutProgramTemplateID
LEFT JOIN WorkoutProgramExercises wpe ON wpd.WorkoutProgramDayID = wpe.WorkoutProgramDayID
WHERE c.IsActive = 1
GROUP BY c.CompanyID, c.CompanyName
ORDER BY c.CompanyName

PRINT ''
PRINT '=== TEST TAMAMLANDI ==='

-- 7. Sonuç özeti
DECLARE @TotalIssues INT = 0

-- Sorun sayısını hesapla
IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE Name = N'CompanyID' AND Object_ID = Object_ID(N'WorkoutProgramDays'))
    SET @TotalIssues = @TotalIssues + 1

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE Name = N'CompanyID' AND Object_ID = Object_ID(N'WorkoutProgramExercises'))
    SET @TotalIssues = @TotalIssues + 1

IF NOT EXISTS (SELECT 1 FROM sys.foreign_keys WHERE name = 'FK_WorkoutProgramDays_Companies')
    SET @TotalIssues = @TotalIssues + 1

IF NOT EXISTS (SELECT 1 FROM sys.foreign_keys WHERE name = 'FK_WorkoutProgramExercises_Companies')
    SET @TotalIssues = @TotalIssues + 1

SET @TotalIssues = @TotalIssues + @InvalidDays + @InvalidExercises + @NullCompanyDays + @NullCompanyExercises + @CrossCompanyDays + @CrossCompanyExercises

PRINT ''
IF @TotalIssues = 0
BEGIN
    PRINT '🎉 SONUÇ: TÜM TESTLER BAŞARILI!'
    PRINT '✅ Çok kiracılı sistem güvenliği sağlanmış'
    PRINT '✅ Veri bütünlüğü korunmuş'
    PRINT '✅ Performans optimizasyonları aktif'
    PRINT ''
    PRINT 'Sistem production''a hazır! 🚀'
END
ELSE
BEGIN
    PRINT '⚠️ SONUÇ: ' + CAST(@TotalIssues AS VARCHAR(10)) + ' SORUN TESPİT EDİLDİ!'
    PRINT '❌ Migration''ları tekrar kontrol edin'
    PRINT '❌ Sorunları çözmeden production''a çıkmayın'
    PRINT ''
    PRINT 'Geliştirici ekibi ile iletişime geçin! 📞'
END

GO
