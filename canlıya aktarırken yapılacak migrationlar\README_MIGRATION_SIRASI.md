# GymProject Production Migration Sırası

Bu dosya production veritabanında çalıştırılması gereken migration'ların doğru sırasını belirtir.

## Migration Çalıştırma Sırası

### 1. Temel Sistem Migration'ları
```sql
-- 1. <PERSON><PERSON><PERSON><PERSON> (temel veriler)
ExerciseSystemMigration.sql
ExerciseSystemSeedData.sql

-- 2. Antrenman programı sistemi
WorkoutProgramSystemMigration.sql

-- 3. Üye-Program atama sistemi
MemberWorkoutProgramsMigration.sql
MemberWorkoutProgramsView.sql
```

### 2. Kullanıcı Sistemi Güncellemeleri
```sql
-- 4. <PERSON>ye sistemi güncellemeleri
MemberUserIdMigration.sql
MemberRoleMigration.sql

-- 5. Kullanıcı profil sistemi
ProfileImageMigration.sql
AddRequirePasswordChangeToUser.sql
```

### 3. Düzeltme Migration'ları (Hotfix)
```sql
-- 6. AssignedDate constraint düzeltmesi (ÖNEMLİ!)
MemberWorkoutProgramsConstraintFix.sql

-- 7. Çok kiracılı sistem güvenlik düzeltmesi (KRİTİK!)
WorkoutProgramMultiTenantFix.sql
```

## Önemli Notlar

### ⚠️ Kritik Düzeltmeler

#### 1. MemberWorkoutProgramsConstraintFix.sql
Bu migration **mutlaka** çalıştırılmalıdır çünkü:
- Production'da "İlk deneme hata, ikinci deneme başarılı" sorununu çözer
- Client-server zaman farkından kaynaklanan constraint ihlallerini önler
- Kullanıcı deneyimini önemli ölçüde iyileştirir

#### 2. WorkoutProgramMultiTenantFix.sql (KRİTİK!)
Bu migration **acilen** çalıştırılmalıdır çünkü:
- **VERİ GÜVENLİĞİ** riski var!
- Salon A, Salon B'nin antrenman programlarını görebilir/değiştirebilir
- KVKV ihlali riski
- Çok kiracılı sistem güvenliğini sağlar
- WorkoutProgramDay ve WorkoutProgramExercise tablolarına CompanyID ekler

### Migration Öncesi Kontroller
1. **Backup alın**: Her migration öncesi veritabanı backup'ı alın
2. **Test ortamında deneyin**: Önce test veritabanında çalıştırın
3. **Sıralı çalıştırın**: Migration'ları belirtilen sırada çalıştırın
4. **Hata kontrolü**: Her migration sonrası hata kontrolü yapın

### Migration Sonrası Kontroller
1. **Constraint kontrolü**: Tüm constraint'lerin çalıştığını kontrol edin
2. **Index kontrolü**: Performance index'lerinin oluştuğunu kontrol edin
3. **Veri bütünlüğü**: Foreign key ilişkilerini kontrol edin
4. **Uygulama testi**: Backend ve frontend'in çalıştığını test edin
5. **Çok kiracılı test**: Farklı salonların birbirinin verilerini göremediğini test edin

## Sorun Giderme

### WorkoutProgramMultiTenantFix.sql Hataları
Eğer bu migration hata verirse:
1. Mevcut verileri kontrol edin: `SELECT * FROM WorkoutProgramDays WHERE CompanyID IS NULL`
2. Orphan kayıtları kontrol edin: Migration otomatik kontrol eder
3. Foreign key hatası: Companies tablosunun varlığını kontrol edin

### MemberWorkoutProgramsConstraintFix.sql Hataları
Eğer bu migration hata verirse:
1. Mevcut constraint'leri kontrol edin: `SELECT * FROM sys.check_constraints WHERE object_id = OBJECT_ID('MemberWorkoutPrograms')`
2. Geçersiz verileri kontrol edin: `SELECT * FROM MemberWorkoutPrograms WHERE AssignedDate > GETDATE()`
3. Manuel düzeltme gerekirse: `UPDATE MemberWorkoutPrograms SET AssignedDate = GETDATE() WHERE AssignedDate > GETDATE()`

### Genel Migration Hataları
1. **Foreign Key Hataları**: Bağımlı tabloların varlığını kontrol edin
2. **Duplicate Key Hataları**: Mevcut verilerde çakışma olup olmadığını kontrol edin
3. **Permission Hataları**: SQL Server'da yeterli yetkiye sahip olduğunuzdan emin olun

## Güvenlik Testi

Migration'lar tamamlandıktan sonra aşağıdaki testleri yapın:

### Çok Kiracılı Sistem Testi
1. **Salon A** ile login olun
2. Antrenman programı oluşturun
3. **Salon B** ile login olun
4. Salon A'nın programını göremediğinizi kontrol edin
5. Kendi programınızı oluşturun
6. Salon A'ya geri dönün ve Salon B'nin programını göremediğinizi kontrol edin

### Veri Güvenliği Testi
1. Database'de direkt sorgu ile kontrol edin:
```sql
-- Her WorkoutProgramDay'in doğru CompanyID'ye sahip olduğunu kontrol et
SELECT wpd.*, wpt.CompanyID as TemplateCompanyID 
FROM WorkoutProgramDays wpd
JOIN WorkoutProgramTemplates wpt ON wpd.WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID
WHERE wpd.CompanyID != wpt.CompanyID

-- Her WorkoutProgramExercise'in doğru CompanyID'ye sahip olduğunu kontrol et
SELECT wpe.*, wpd.CompanyID as DayCompanyID 
FROM WorkoutProgramExercises wpe
JOIN WorkoutProgramDays wpd ON wpe.WorkoutProgramDayID = wpd.WorkoutProgramDayID
WHERE wpe.CompanyID != wpd.CompanyID
```

## İletişim
Migration sırasında sorun yaşarsanız geliştirici ekibi ile iletişime geçin.
