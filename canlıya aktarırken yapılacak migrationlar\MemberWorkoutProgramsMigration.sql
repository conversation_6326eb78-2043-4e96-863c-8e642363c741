-- Üye Program Atama Sistemi Migration Script
-- Bu script üyelere antrenman programı atama sistemini oluşturur
-- MemberWorkoutPrograms: Üye-Program ilişki tablosu

USE [GymProject]
GO

-- 1. MemberWorkoutPrograms Tablosu (Üye-Program Atamaları)
CREATE TABLE [dbo].[MemberWorkoutPrograms](
    [MemberWorkoutProgramID] [int] IDENTITY(1,1) NOT NULL,
    [MemberID] [int] NOT NULL,
    [WorkoutProgramTemplateID] [int] NOT NULL,
    [CompanyID] [int] NOT NULL,
    [AssignedDate] [datetime2](7) NOT NULL DEFAULT(GETDATE()), -- <PERSON><PERSON> tarihi (server zamanı)
    [StartDate] [datetime2](7) NOT NULL, -- Program başlangıç tarihi
    [EndDate] [datetime2](7) NULL, -- Program bitiş tarihi (opsiyonel)
    [Notes] [nvarchar](1000) NULL, -- Atama notları
    [IsActive] [bit] NOT NULL DEFAULT(1), -- Aktif mi?
    [CreationDate] [datetime2](7) NULL DEFAULT(GETDATE()),
    [DeletedDate] [datetime2](7) NULL,
    [UpdatedDate] [datetime2](7) NULL,
    CONSTRAINT [PK_MemberWorkoutPrograms] PRIMARY KEY CLUSTERED ([MemberWorkoutProgramID] ASC),
    CONSTRAINT [FK_MemberWorkoutPrograms_Members] FOREIGN KEY([MemberID]) 
        REFERENCES [dbo].[Members] ([MemberID]) ON DELETE CASCADE,
    CONSTRAINT [FK_MemberWorkoutPrograms_WorkoutProgramTemplates] FOREIGN KEY([WorkoutProgramTemplateID]) 
        REFERENCES [dbo].[WorkoutProgramTemplates] ([WorkoutProgramTemplateID]) ON DELETE CASCADE,
    CONSTRAINT [FK_MemberWorkoutPrograms_Companies] FOREIGN KEY([CompanyID]) 
        REFERENCES [dbo].[Companies] ([CompanyID])
)
GO

-- 2. PERFORMANS İNDEXLERİ (10.000+ kullanıcı için optimizasyon)

-- Üye bazlı aktif programlar (en çok kullanılacak sorgu)
CREATE NONCLUSTERED INDEX [IX_MemberWorkoutPrograms_MemberID_IsActive] 
ON [dbo].[MemberWorkoutPrograms] ([MemberID], [IsActive])
INCLUDE ([WorkoutProgramTemplateID], [StartDate], [EndDate], [AssignedDate])
GO

-- Şirket bazlı atamalar (admin paneli için)
CREATE NONCLUSTERED INDEX [IX_MemberWorkoutPrograms_CompanyID_IsActive] 
ON [dbo].[MemberWorkoutPrograms] ([CompanyID], [IsActive])
INCLUDE ([MemberID], [WorkoutProgramTemplateID], [AssignedDate])
GO

-- Program bazlı atamalar (hangi programa kaç kişi atanmış)
CREATE NONCLUSTERED INDEX [IX_MemberWorkoutPrograms_TemplateID_IsActive] 
ON [dbo].[MemberWorkoutPrograms] ([WorkoutProgramTemplateID], [IsActive])
INCLUDE ([MemberID], [AssignedDate])
GO

-- Tarih bazlı sorgular için (atama geçmişi)
CREATE NONCLUSTERED INDEX [IX_MemberWorkoutPrograms_AssignedDate] 
ON [dbo].[MemberWorkoutPrograms] ([AssignedDate] DESC)
WHERE [IsActive] = 1
GO

-- Mobil API için User-Member-Program ilişkisi (composite index)
CREATE NONCLUSTERED INDEX [IX_MemberWorkoutPrograms_Member_Company_Active] 
ON [dbo].[MemberWorkoutPrograms] ([MemberID], [CompanyID], [IsActive])
INCLUDE ([WorkoutProgramTemplateID], [StartDate], [EndDate], [Notes])
GO

-- 3. KAPSAM KONTROLÜ (Constraint'ler)

-- StartDate EndDate'den küçük olmalı (eğer EndDate varsa)
ALTER TABLE [dbo].[MemberWorkoutPrograms]
ADD CONSTRAINT [CK_MemberWorkoutPrograms_DateRange] 
CHECK ([EndDate] IS NULL OR [StartDate] <= [EndDate])
GO

-- AssignedDate gelecekte olamaz (5 saniye tolerans ile client-server zaman farkı için)
ALTER TABLE [dbo].[MemberWorkoutPrograms]
ADD CONSTRAINT [CK_MemberWorkoutPrograms_AssignedDate]
CHECK ([AssignedDate] <= DATEADD(SECOND, 5, GETDATE()))
GO

-- 4. ÇOK KİRACILI SİSTEM GÜVENLİK DÜZELTMELERİ

PRINT 'Çok kiracılı sistem güvenlik düzeltmeleri uygulanıyor...'

-- WorkoutProgramDays tablosuna CompanyID ekleme
IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE Name = N'CompanyID' AND Object_ID = Object_ID(N'WorkoutProgramDays'))
BEGIN
    PRINT 'WorkoutProgramDays tablosuna CompanyID alanı ekleniyor...'

    ALTER TABLE [dbo].[WorkoutProgramDays]
    ADD [CompanyID] [int] NULL,
        [DeletedDate] [datetime2](7) NULL,
        [UpdatedDate] [datetime2](7) NULL

    -- Mevcut verilere CompanyID atama
    UPDATE wpd
    SET wpd.CompanyID = wpt.CompanyID
    FROM [dbo].[WorkoutProgramDays] wpd
    INNER JOIN [dbo].[WorkoutProgramTemplates] wpt ON wpd.WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID
    WHERE wpd.CompanyID IS NULL

    -- CompanyID'yi NOT NULL yap
    ALTER TABLE [dbo].[WorkoutProgramDays]
    ALTER COLUMN [CompanyID] [int] NOT NULL

    -- Foreign key ekle
    ALTER TABLE [dbo].[WorkoutProgramDays]
    ADD CONSTRAINT [FK_WorkoutProgramDays_Companies] FOREIGN KEY([CompanyID])
        REFERENCES [dbo].[Companies] ([CompanyID])

    PRINT 'WorkoutProgramDays güncellendi.'
END

-- WorkoutProgramExercises tablosuna CompanyID ekleme
IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE Name = N'CompanyID' AND Object_ID = Object_ID(N'WorkoutProgramExercises'))
BEGIN
    PRINT 'WorkoutProgramExercises tablosuna CompanyID alanı ekleniyor...'

    ALTER TABLE [dbo].[WorkoutProgramExercises]
    ADD [CompanyID] [int] NULL,
        [DeletedDate] [datetime2](7) NULL,
        [UpdatedDate] [datetime2](7) NULL

    -- Mevcut verilere CompanyID atama
    UPDATE wpe
    SET wpe.CompanyID = wpt.CompanyID
    FROM [dbo].[WorkoutProgramExercises] wpe
    INNER JOIN [dbo].[WorkoutProgramDays] wpd ON wpe.WorkoutProgramDayID = wpd.WorkoutProgramDayID
    INNER JOIN [dbo].[WorkoutProgramTemplates] wpt ON wpd.WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID
    WHERE wpe.CompanyID IS NULL

    -- CompanyID'yi NOT NULL yap
    ALTER TABLE [dbo].[WorkoutProgramExercises]
    ALTER COLUMN [CompanyID] [int] NOT NULL

    -- Foreign key ekle
    ALTER TABLE [dbo].[WorkoutProgramExercises]
    ADD CONSTRAINT [FK_WorkoutProgramExercises_Companies] FOREIGN KEY([CompanyID])
        REFERENCES [dbo].[Companies] ([CompanyID])

    PRINT 'WorkoutProgramExercises güncellendi.'
END

-- 5. ÇOK KİRACILI SİSTEM PERFORMANS İNDEXLERİ

-- WorkoutProgramDays için company bazlı index
IF NOT EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'IX_WorkoutProgramDays_CompanyID_TemplateID')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_WorkoutProgramDays_CompanyID_TemplateID]
    ON [dbo].[WorkoutProgramDays] ([CompanyID], [WorkoutProgramTemplateID])
    INCLUDE ([DayNumber], [DayName], [IsRestDay])

    PRINT 'WorkoutProgramDays çok kiracılı index eklendi.'
END

-- WorkoutProgramExercises için company bazlı index
IF NOT EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'IX_WorkoutProgramExercises_CompanyID_DayID')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_WorkoutProgramExercises_CompanyID_DayID]
    ON [dbo].[WorkoutProgramExercises] ([CompanyID], [WorkoutProgramDayID])
    INCLUDE ([ExerciseType], [ExerciseID], [OrderIndex])

    PRINT 'WorkoutProgramExercises çok kiracılı index eklendi.'
END

-- 6. AssignedDate CONSTRAINT DÜZELTMESİ

-- Mevcut constraint'i kontrol et ve gerekirse güncelle
IF EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_MemberWorkoutPrograms_AssignedDate')
BEGIN
    DECLARE @ConstraintDefinition NVARCHAR(MAX)
    SELECT @ConstraintDefinition = definition
    FROM sys.check_constraints
    WHERE name = 'CK_MemberWorkoutPrograms_AssignedDate'

    -- Eğer eski constraint ise (tolerans yoksa) güncelle
    IF @ConstraintDefinition NOT LIKE '%DATEADD%'
    BEGIN
        PRINT 'AssignedDate constraint güncelleniyor (client-server zaman farkı için)...'

        -- Eski constraint'i kaldır
        ALTER TABLE [dbo].[MemberWorkoutPrograms]
        DROP CONSTRAINT [CK_MemberWorkoutPrograms_AssignedDate]

        -- Yeni constraint'i ekle (5 saniye tolerans ile)
        ALTER TABLE [dbo].[MemberWorkoutPrograms]
        ADD CONSTRAINT [CK_MemberWorkoutPrograms_AssignedDate]
        CHECK ([AssignedDate] <= DATEADD(SECOND, 5, GETDATE()))

        PRINT 'AssignedDate constraint güncellendi.'
    END
END

PRINT 'Üye Program Atama Sistemi migration tamamlandı!'
PRINT 'Oluşturulan tablolar:'
PRINT '- MemberWorkoutPrograms (Üye-Program atamaları)'
PRINT '- WorkoutProgramDays ve WorkoutProgramExercises çok kiracılı güvenlik eklendi'
PRINT 'Performans indexleri ve constraint''ler eklendi.'
PRINT 'Mobil API için optimizasyonlar hazır.'
PRINT 'Çok kiracılı sistem güvenliği sağlandı.'
GO
